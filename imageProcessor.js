const OpenAI = require('openai');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { EmbedBuilder } = require('discord.js');
const sharp = require('sharp');

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const userCooldowns = new Map();

function checkCooldown(userId) {
  const cooldownSeconds = parseInt(process.env.COOLDOWN_SECONDS) || 60; // Default 60 seconds if not set
  const now = Date.now();
  const lastUse = userCooldowns.get(userId) || 0;
  const timePassed = (now - lastUse) / 1000; // Convert to seconds

  if (timePassed < cooldownSeconds) {
    return {
      onCooldown: true,
      remainingSeconds: Math.ceil(cooldownSeconds - timePassed)
    };
  }

  userCooldowns.set(userId, now);
  return {
    onCooldown: false,
    remainingSeconds: 0
  };
}



// Merge multiple images into a single composite image
async function mergeImages(imageBuffers) {
  if (imageBuffers.length === 0) return null;
  if (imageBuffers.length === 1) return imageBuffers[0];

  try {
    // Get dimensions of all images
    const imageInfos = await Promise.all(
      imageBuffers.map(async (buffer) => {
        const metadata = await sharp(buffer).metadata();
        return { buffer, width: metadata.width, height: metadata.height };
      })
    );

    // Calculate total height and max width
    const maxWidth = Math.max(...imageInfos.map(info => info.width));
    const totalHeight = imageInfos.reduce((sum, info) => sum + info.height, 0);

    // Create composite image
    let composite = sharp({
      create: {
        width: maxWidth,
        height: totalHeight,
        channels: 3,
        background: { r: 255, g: 255, b: 255 }
      }
    });

    // Prepare overlay operations
    const overlays = [];
    let currentTop = 0;

    for (const info of imageInfos) {
      overlays.push({
        input: info.buffer,
        top: currentTop,
        left: 0
      });
      currentTop += info.height;
    }

    // Apply overlays and get the result
    const mergedBuffer = await composite
      .composite(overlays)
      .png()
      .toBuffer();

    return mergedBuffer;
  } catch (error) {
    console.error('Error merging images:', error);
    throw error;
  }
}

// Check if two images are identical by comparing their buffers
function areImagesIdentical(buffer1, buffer2) {
  return Buffer.compare(buffer1, buffer2) === 0;
}

// Process each screenshot
async function processScreenshots(attachments) {
  const screenshotsDir = path.join(process.cwd(), 'screenshots');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir);
  }

  const allImageBuffers = [];
  const savedImagePaths = [];
  let identicalImageCount = 0;

  console.log(`Downloading and merging ${attachments.size} images...`);

  // Download all images and check for duplicates
  for (const [id, attachment] of attachments) {
    try {
      console.log(`Downloading attachment ${id}...`);

      // Download image
      const response = await axios.get(attachment.url, { responseType: 'arraybuffer' });
      const imageBuffer = Buffer.from(response.data);

      // Save image locally for cleanup later
      const imagePath = path.join(screenshotsDir, `screenshot_${Date.now()}_${id}.png`);
      fs.writeFileSync(imagePath, imageBuffer);
      savedImagePaths.push(imagePath);

      // Check for identical images
      const isIdentical = allImageBuffers.some(existingBuffer =>
        areImagesIdentical(imageBuffer, existingBuffer)
      );

      if (isIdentical) {
        identicalImageCount++;
        console.log(`⚠️ Attachment ${id} is identical to a previous image - skipping duplicate`);
      } else {
        allImageBuffers.push(imageBuffer);
        console.log(`✓ Attachment ${id} added to merge queue`);
      }
    } catch (error) {
      console.error(`Error processing attachment ${id}:`, error);
    }
  }

  console.log(`Merging ${allImageBuffers.length} unique images out of ${attachments.size} total`);
  if (identicalImageCount > 0) {
    console.log(`Skipped ${identicalImageCount} identical duplicate images`);
  }

  // If no images found, return empty results
  if (allImageBuffers.length === 0) {
    cleanupScreenshots(savedImagePaths);
    return { rounds: [], identicalCount: identicalImageCount };
  }

  try {
    // Merge ALL images into a single composite image
    console.log('Merging all images...');
    const mergedImageBuffer = await mergeImages(allImageBuffers);

    if (!mergedImageBuffer) {
      console.log('Failed to merge images');
      cleanupScreenshots(savedImagePaths);
      return { rounds: [], identicalCount: identicalImageCount };
    }

    // Save merged image to screenshots folder for inspection
    const mergedImagePath = path.join(screenshotsDir, `merged_image_${Date.now()}.png`);
    fs.writeFileSync(mergedImagePath, mergedImageBuffer);
    savedImagePaths.push(mergedImagePath);
    console.log(`Merged image saved to: ${mergedImagePath}`);

    // Process the merged image - GPT-4o will intelligently identify valid game rounds
    console.log('Processing merged image with AI (GPT-4o will identify valid game rounds)...');
    const base64MergedImage = mergedImageBuffer.toString('base64');
    const result = await processMultipleRounds(base64MergedImage, allImageBuffers.length);

    // Clean up saved images (including merged image)
    cleanupScreenshots(savedImagePaths);

    // Return results in the expected format
    if (result && result.rounds && result.rounds.length > 0) {
      return { rounds: result.rounds, identicalCount: identicalImageCount };
    } else {
      return { rounds: [], identicalCount: identicalImageCount };
    }

  } catch (error) {
    console.error('Error processing merged images:', error);
    cleanupScreenshots(savedImagePaths);
    return { rounds: [], identicalCount: identicalImageCount };
  }
}

// cleanup screenshots
function cleanupScreenshots(filePaths) {
  if (!filePaths || filePaths.length === 0) return;

  filePaths.forEach(filePath => {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.error(`Failed to delete file ${filePath}:`, error.message);
    }
  });
}

// clean username
function cleanUsername(username) {
  if (!username) return '';

  let cleaned = username
    .replace(/卍/g, "")
    .replace(/卐/g, "")
    .replace(/!/g, "I")
    .replace(/\|/g, "I")
    .replace(/\l/g, "I");

  cleaned = cleaned.replace(/(.)\1{9,}/g, function(match, char) {
    return char.repeat(9);
  });

  return cleaned;
}

//send merged image to gpt for multiple rounds processing
async function processMultipleRounds(base64Image, expectedRounds) {
  try {
    const messages = [
      {
        role: "system",
        content: `You are an intelligent image data extractor for game scoreboards. This merged image may contain up to ${expectedRounds} images stacked vertically. Some images may be valid game scoreboards with "DMG" and "EDA" columns, while others may be invalid (non-game images, chat screenshots, etc.).

ONLY extract data from images that contain valid game scoreboards with both "DMG" and "EDA" columns in a table format. Ignore any invalid images completely.

CRITICAL FOR TEAM COLORS: Look at each player's ROW BACKGROUND COLOR to determine their team:
- If the entire row has a PURPLE/VIOLET background → team_color: "purple"
- If the entire row has a GOLD/YELLOW background → team_color: "gold"
Do NOT use ping icons, player positioning, or any other indicators - ONLY the row background color.

Return a JSON object with a 'rounds' array, where each round contains a 'players' array with player data from valid game scoreboards only.`
      },
      {
        role: "user",
        content: [
          {
            type: "text",
            text: `This merged image may contain up to ${expectedRounds} images stacked vertically. Some may be valid game scoreboards, others may be invalid (chat, menus, etc.).

ONLY process images that show game scoreboards with BOTH "DMG" and "EDA" columns in a table format. For each VALID scoreboard found, extract for each player row:
1. 'name': The player username (visible in the leftmost column)
2. 'team_color': CRITICAL - Look at the ENTIRE ROW background color, NOT just the ping icon. If the player's row has a PURPLE/VIOLET background, they are "purple" team. If the row has a GOLD/YELLOW background, they are "gold" team. The row background color determines the team.
3. 'EDA': The exact 3 numbers from the EDA column (like "7 5 4")

IGNORE any images that don't contain valid game scoreboards. Return JSON with 'rounds' array containing player data ONLY from valid scoreboards.

Format as JSON: {"rounds": [{"players": [{"name": "PlayerName", "team_color": "purple", "EDA": "7 5 4"}]}]}

CRITICAL: Make sure to extract the actual player names from the leftmost column. Do not leave names empty or undefined.`
          },
          {
            type: "image_url",
            image_url: {
              url: `data:image/png;base64,${base64Image}`
            }
          }
        ]
      }
    ];

    console.log('Sending merged image request to OpenAI...');
    const chatCompletion = await openai.chat.completions.create({
      model: "gpt-4o", // Using the more capable model for complex multi-round processing
      messages: messages,
      response_format: { type: "json_object" },
      max_tokens: 3000 // Increased for multiple rounds
    });

    const response = JSON.parse(chatCompletion.choices[0].message.content);

    // Debug: Log the full AI response
    console.log('Full AI response:', JSON.stringify(response, null, 2));

    if (!response.rounds || !Array.isArray(response.rounds)) {
      console.log('Invalid response format, missing rounds array:', response);
      return { rounds: [] };
    }

    // Process each round's players
    const processedRounds = response.rounds.map((round, roundIndex) => {
      if (!round.players || !Array.isArray(round.players)) {
        console.log(`Round ${roundIndex + 1} has no valid players array`);
        return { players: [] };
      }

      const processedPlayers = round.players.map(player => {
        let kills = 0;
        let deaths = 0;

        // Debug: Log the raw player data
        console.log(`Round ${roundIndex + 1} - Raw player data:`, JSON.stringify(player));

        if (player.EDA || player.eda) {
          const edaString = (player.EDA || player.eda).toString().trim();
          const edaValues = edaString.split(/\s+/);

          if (edaValues.length >= 2) {
            kills = parseInt(edaValues[0]) || 0;
            deaths = parseInt(edaValues[1]) || 0;
            console.log(`Round ${roundIndex + 1} - Player ${player.name}: EDA "${edaString}" → Kills: ${kills}, Deaths: ${deaths}`);
          }
        }

        const cleanedName = cleanUsername(player.name || '');
        const result = {
          name: cleanedName,
          team_color: player.team_color,
          kills: kills,
          deaths: deaths
        };

        console.log(`Round ${roundIndex + 1} - Processed player:`, JSON.stringify(result));
        return result;
      });

      return { players: processedPlayers };
    });

    console.log(`Processed ${processedRounds.length} rounds from merged image`);
    return { rounds: processedRounds };
  } catch (error) {
    console.error('Error in processMultipleRounds:', error);
    return { rounds: [] };
  }
}

//send to gpt (legacy function for single images)
async function processOneImage(base64Image) {
  try {
    const messages = [
      {
        role: "system",
        content: "You are an image data extractor. First, check for the EXACT words \"DMG\" and \"EDA\" in the image. It should be in a scoreboard TABLE format, with a \"DMG\" and \"EDA\" column. If this is not present, return an error message. Ensure the screenshot is a scoreboard TABLE before proceeding! For each player listed in the game scoreboard, extract their username (not the @username, the name above that), team_color, and EDA values. ⚠️ To determine team_color, look ONLY at the background color directly behind the ping icon — this is the small bar at the far-right end of the player's row. A GOLD/YELLOW background means the player is on the gold team, and a PURPLE/VIOLET background means the player is on the purple team. ❌ Do NOT use the player's row position, order on the list, or the color behind their name, EDA, or DMG. ⚠️ The players are NOT grouped by team, and the scoreboard is intentionally misleading — team position is RANDOM. There are always an equal number of gold and purple players. Count each team to confirm. If a player appears grouped with the wrong team but has a gold background behind the ping, they are on the GOLD team — trust the ping background ONLY. Extract exact EDA numbers in order (kills assists deaths), like: '5 3 2'."
      },
      {
        role: "user",
        content: [
          {
            type: "text",
            text: "First, check the image for the words \"DMG\" and \"EDA\" in a scoreboard TABLE format. If this is not present, return an error message. Then, for each player in this screenshot, extract: their name, team_color (purple or gold based on background color behind user's ping), and the exact values in the EDA column (just the 3 numbers, seperated by spaces. like this; 5 9 3, or 10 8 3). Format as JSON with a 'players' array. Important: team_color MUST NOT be determined by player order or row groupings. Trust only the ping background color. There will be an equal number of gold and purple players, do not output an uneven number of players for either team color."
          },
          {
            type: "image_url",
            image_url: {
              url: `data:image/png;base64,${base64Image}`
            }
          }
        ]
      }
    ];

    console.log('Sending request to OpenAI...');
    const chatCompletion = await openai.chat.completions.create({
      model: "gpt-4.1",
      messages: messages,
      response_format: { type: "json_object" },
      max_tokens: 1000
    });

    const response = JSON.parse(chatCompletion.choices[0].message.content);

    if (!response.players || !Array.isArray(response.players)) {
      console.log('Invalid response format, missing players array:', response);
      if (response.data && Array.isArray(response.data)) {
        response.players = response.data;
      } else {
        return { players: [] };
      }
    }

    // Process the EDA values to kills and deaths
    const processedPlayers = response.players.map(player => {
      let kills = 0;
      let deaths = 0;

      if (player.EDA || player.eda) {
        const edaString = (player.EDA || player.eda).toString().trim();
        const edaValues = edaString.split(/\s+/);

        if (edaValues.length >= 2) {
          kills = parseInt(edaValues[0]) || 0;
          deaths = parseInt(edaValues[1]) || 0;
          console.log(`Player ${player.name}: EDA "${edaString}" → Kills: ${kills}, Deaths: ${deaths}`);
        }
      }

      return {
        name: cleanUsername(player.name),
        team_color: player.team_color,
        kills: kills,
        deaths: deaths
      };
    });

    return { players: processedPlayers };
  } catch (error) {
    console.error('Error in processOneImage:', error);
    return { players: [] };
  }
}

//combine results
function combineResults(screenshotResults) {
  // Handle new format with identicalCount
  let rounds = [];
  let identicalCount = 0;

  if (screenshotResults && screenshotResults.rounds) {
    rounds = screenshotResults.rounds;
    identicalCount = screenshotResults.identicalCount || 0;
  } else if (Array.isArray(screenshotResults)) {
    // Legacy format - array of rounds
    rounds = screenshotResults;
  }

  if (!rounds || rounds.length === 0) {
    return {
      error: "No valid game results found in the provided screenshots.",
      identicalCount: identicalCount
    };
  }

  const playerStats = new Map();
  const nameVariants = new Map();

  // Process all rounds
  rounds.forEach(result => {
    if (result.players) {
      result.players.forEach(player => {
        const name = cleanUsername(player.name);
        const teamColor = player.team_color || '';
        const kills = player.kills || 0;
        const deaths = player.deaths || 0;

        if (!name || !teamColor) {
          console.log(`Skipping player with incomplete data:`, player);
          return;
        }

        // check for variants
        let matchedName = findSimilarName(name, Array.from(playerStats.keys()));

        if (matchedName && matchedName !== name) {
          console.log(`Matched "${name}" as a variant of "${matchedName}"`);

          if (!nameVariants.has(matchedName)) {
            nameVariants.set(matchedName, []);
          }
          nameVariants.get(matchedName).push(name);

          // Use the matched name for stats
          const existing = playerStats.get(matchedName);
          existing.kills += kills;
          existing.deaths += deaths;
          existing.rounds += 1;

          // Track team colors
          if (!existing.teamColors.includes(teamColor.toLowerCase())) {
            existing.teamColors.push(teamColor.toLowerCase());
          }

        } else {
          const actualName = matchedName || name;

          const existing = playerStats.get(actualName) || {
            name: actualName,
            teamColor: teamColor,
            kills: 0,
            deaths: 0,
            rounds: 0,
            teamColors: []
          };

          existing.kills += kills;
          existing.deaths += deaths;
          existing.rounds += 1;

          // Track team colors
          if (!existing.teamColors.includes(teamColor.toLowerCase())) {
            existing.teamColors.push(teamColor.toLowerCase());
          }

          playerStats.set(actualName, existing);

        }
      });
    }
  });


  const purpleTeam = [];
  const goldTeam = [];

  let teamSwitchCount = 0;

  // Sort players into teams
  playerStats.forEach(player => {
    // Check for multiple teams
    const isPurple = player.teamColors.some(color => color.includes('purple'));
    const isGold = player.teamColors.some(color => color.includes('gold') || color.includes('yellow'));
    const teamSwitcher = isPurple && isGold;
    player.teamSwitcher = teamSwitcher;
    if (teamSwitcher) teamSwitchCount++;

    // Add rounds played to player data
    const gamesPlayed = player.rounds;

    // Group player by their last known team
    const color = player.teamColor.toLowerCase();
    if (color.includes('purple')) {
      purpleTeam.push({
        name: player.name,
        kills: player.kills,
        deaths: player.deaths,
        teamSwitcher: teamSwitcher,
        gamesPlayed: gamesPlayed,
        totalGames: rounds.length
      });
    } else if (color.includes('gold') || color.includes('yellow')) {
      goldTeam.push({
        name: player.name,
        kills: player.kills,
        deaths: player.deaths,
        teamSwitcher: teamSwitcher,
        gamesPlayed: gamesPlayed,
        totalGames: rounds.length
      });
    } else {
      console.log(`Player ${player.name} has unknown team color: ${player.teamColor}`);
    }
  });

  console.log(`Purple team has ${purpleTeam.length} players, Gold team has ${goldTeam.length} players`);

  return {
    title: `Match Results (from ${rounds.length} rounds)`,
    teams: [
      {
        name: "Purple Team",
        players: purpleTeam
      },
      {
        name: "Gold Team",
        players: goldTeam
      }
    ],
    teamSwitchCount: teamSwitchCount,
    identicalCount: identicalCount
  };
}

// Update findSimilarName to better handle repetitive characters
function findSimilarName(name, existingNames) {
  if (existingNames.includes(name)) {
    return name;
  }

  const nameLower = name.toLowerCase();

  // Case-insensitive match
  const caseInsensitiveMatch = existingNames.find(n => n.toLowerCase() === nameLower);
  if (caseInsensitiveMatch) {
    return caseInsensitiveMatch;
  }


  const simplifyName = (str) => str.toLowerCase().replace(/(.)\1{2,}/g, '$1$1');
  const simplifiedName = simplifyName(name);

  const patternMatch = existingNames.find(existingName => {
    return simplifyName(existingName) === simplifiedName;
  });

  if (patternMatch) {
    return name.length <= patternMatch.length ? name : patternMatch;
  }

  // Regular similarity checks (existing code)
  const similarNameMatch = existingNames.find(existingName => {
    // If one is a subset of the other
    if (existingName.toLowerCase().includes(nameLower) ||
        nameLower.includes(existingName.toLowerCase())) {
      return true;
    }

    const distance = levenshteinDistance(nameLower, existingName.toLowerCase());

    // If names are short, allow 1 character difference
    // For longer names, allow up to 2 character differences
    const threshold = Math.min(existingName.length, name.length) <= 5 ? 1 : 2;

    return distance <= threshold;
  });

  // When we have a match, prefer the shorter version
  if (similarNameMatch) {
    return name.length <= similarNameMatch.length ? name : similarNameMatch;
  }

  return null;
}

function levenshteinDistance(a, b) {
  if (a.length === 0) return b.length;
  if (b.length === 0) return a.length;

  const matrix = [];

  for (let i = 0; i <= b.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= a.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= b.length; i++) {
    for (let j = 1; j <= a.length; j++) {
      if (b.charAt(i - 1) === a.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }

  return matrix[b.length][a.length];
}

//final embed
function createResultsEmbed(resultsData) {
  const embed = new EmbedBuilder()
    .setColor('Green')
    .setTimestamp();

  if (typeof resultsData === 'string') {
    embed.setTitle('Error Processing Results');
    embed.setDescription(`Unable to process response format:\n\`\`\`\n${resultsData.substring(0, 1000)}\n\`\`\``);
    embed.setColor('Orange');
    return embed;
  }

  if (resultsData.error) {
    embed.setTitle('Error Processing Results');
    embed.setDescription(`**Error:** ${resultsData.error}`);
    embed.setColor('Red');
    return embed;
  }

  try {
    embed.setTitle(resultsData.title || "Match Results");

    let description = '';

    if (resultsData.teams && resultsData.teams.length > 0) {
      // Find the team with the player that has the most kills
      let topPlayerGlobal = null;
      let topPlayerTeamIndex = -1;

      // Find the player with most kills overall
      resultsData.teams.forEach((team, teamIndex) => {
        if (team.players && team.players.length > 0) {
          team.players.forEach(player => {
            if (player.kills !== undefined) {
              if (!topPlayerGlobal || player.kills > topPlayerGlobal.kills) {
                topPlayerGlobal = player;
                topPlayerTeamIndex = teamIndex;
              }
            }
          });
        }
      });

      // Find player with most kills on opposite team
      let secondTopPlayer = null;

      if (resultsData.teams.length > 1 && topPlayerTeamIndex !== -1) {
        for (let i = 0; i < resultsData.teams.length; i++) {
          if (i === topPlayerTeamIndex) continue;

          const team = resultsData.teams[i];
          if (team.players && team.players.length > 0) {
            team.players.forEach(player => {
              if (player.kills !== undefined) {
                if (!secondTopPlayer || player.kills > secondTopPlayer.kills) {
                  secondTopPlayer = player;
                }
              }
            });
          }
        }
      }

      // highlights
      if (topPlayerGlobal) {
        topPlayerGlobal.highlight = '👑';
      }

      if (secondTopPlayer) {
        secondTopPlayer.highlight = '🥈';
      }

      resultsData.teams.forEach(team => {
        if (!team.players || team.players.length === 0) return;

        description += `**${team.name}:**\n`;

        const sortedPlayers = [...team.players].sort((a, b) => (b.kills || 0) - (a.kills || 0));

        sortedPlayers.forEach(player => {
          let playerLine = '';

          if (player.highlight === '👑') {
            playerLine = `**${player.name}**: `;
          } else {
            playerLine = `${player.name}: `;
          }

          // kills/deaths
          playerLine += `${player.kills}/${player.deaths}`;

          // Add KD ratio
          if (player.deaths > 0) {
            const kd = (player.kills / player.deaths).toFixed(2);
            playerLine += ` (${kd} KD)`;
          } else if (player.kills > 0) {
            playerLine += ` (${player.kills} KD)`;
          } else {
            playerLine += ` (0.00 KD)`;
          }

          // Add games played if not all games
          if (player.gamesPlayed < player.totalGames) {
            playerLine += ` (played ${player.gamesPlayed}/${player.totalGames})`;
          }

          // emoji
          if (player.highlight) {
            playerLine += ` ${player.highlight}`;
          }

          // on multiple teams - only show if 2 or fewer players switched
          if (player.teamSwitcher && resultsData.teamSwitchCount <= 2) {
            playerLine += ` \`🔁\``;
          }

          description += `${playerLine}\n`;
        });

        description += '\n';
      });
    }

    embed.setDescription(description || "No player data found in the response.");

    // Set footer based on conditions
    let footerText = '';
    if (resultsData.teamSwitchCount > 2) {
      footerText = 'Teams may have switched colors during these matches';
    }
    if (resultsData.identicalCount > 0) {
      if (footerText) {
        footerText += ` • ${resultsData.identicalCount} identical image(s) were skipped`;
      } else {
        footerText = `${resultsData.identicalCount} identical image(s) were skipped`;
      }
    }
    if (footerText) {
      embed.setFooter({ text: footerText });
    }

  } catch (error) {
    console.error('Error formatting embed:', error);
    embed.setTitle('Error Formatting Results');
    embed.setDescription(`Error formatting game results:\n\`\`\`\n${error.message}\n\`\`\``);
    embed.setColor('Red');
  }

  return embed;
}

module.exports = {
  processScreenshots,
  cleanupScreenshots,
  processOneImage,
  processMultipleRounds,
  mergeImages,
  areImagesIdentical,
  combineResults,
  findSimilarName,
  levenshteinDistance,
  createResultsEmbed,
  checkCooldown,
  cleanUsername
};