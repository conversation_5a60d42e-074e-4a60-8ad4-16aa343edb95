const { Client, GatewayIntentBits, EmbedBuilder } = require('discord.js');
require('dotenv').config();

// image processing
const {
  processScreenshots,
  combineResults,
  createResultsEmbed,
  checkCooldown
} = require('./imageProcessor');

const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
  ],
});

client.once('ready', () => {
  console.log(`Logged in as ${client.user.tag}`);
});

client.on('messageCreate', async message => {
  if (message.author.bot) return;

  if (message.channel.id === process.env.CHANNEL_ID) {


    var attachments;
    if (!message.attachments.size) {
      // const noAttachmentsEmbed = new EmbedBuilder()
      //   .setDescription('**No attachments found.** Please attach game screenshots when using the `!results` command.')
      //   .setColor('Red');
      // message.reply({ embeds: [noAttachmentsEmbed] });

      if (!message.messageSnapshots.first()) return;
      if (!message.messageSnapshots.first().attachments.size) return;
      attachments = message.messageSnapshots.first().attachments;
    } else {
      attachments = message.attachments;
    }

    //too many attachments
    if (attachments.size > process.env.MAX_ATTACHMENTS) {
      const tooManyAttachmentsEmbed = new EmbedBuilder()
        .setDescription(`**Too many images.** Please limit your results to \`${process.env.MAX_ATTACHMENTS}\` screenshots at a time.`)
        .setColor('Red');
      message.reply({ embeds: [tooManyAttachmentsEmbed] });
      return;
    }

    // Check cooldown
    const cooldownResult = checkCooldown(message.author.id);
    if (cooldownResult.onCooldown) return message.react('⏳');

    const loadingEmbed = new EmbedBuilder()
      .setDescription(`**Validating and merging images... \`${attachments.size}\`**`)
      .setColor('Blue');
    const responseMsg = await message.reply({ embeds: [loadingEmbed] });

    try {
      // Validate, merge, and process images
      const screenshotResults = await processScreenshots(attachments);

      // Combine the results
      const combinedResults = combineResults(screenshotResults);

      const resultsEmbed = createResultsEmbed(combinedResults);
      await responseMsg.edit({ embeds: [resultsEmbed] });

    } catch (error) {
      console.error('Error processing images:', error);

      const errorEmbed = new EmbedBuilder()
        .setDescription(`**Error processing images:** ${error.message}`)
        .setColor('Red');

      await responseMsg.edit({ embeds: [errorEmbed] });
    }
  }
});

client.login(process.env.TOKEN);
